apiVersion: v1
kind: Secret
metadata:
  name: sabeco-app-secrets
type: Opaque
stringData:
  KEYVAULT_URL: your-keyvault-name.vault.azure.net
  SQL_CONNECTION_STRING: Server=tcp:server.database.windows.net,1433;Initial Catalog=database;Persist Security Info=False;User ID=username;Password=password;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;
  COSMOS_CONNECTION_STRING: AccountEndpoint=https://your-cosmos-account.documents.azure.com:443/;AccountKey=your-account-key;
  BLOB_CONNECTION_STRING: DefaultEndpointsProtocol=https;AccountName=your-storage-account;AccountKey=your-account-key;EndpointSuffix=core.windows.net
  ACR_NAME: your-acr-name
  ACR_SUBSCRIPTION: your-subscription-id
  ACR_RG: your-resource-group
  REDIS_CONNECTION_STRING: redis://username:password@host:6379

